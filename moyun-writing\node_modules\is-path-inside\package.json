{"name": "is-path-inside", "version": "3.0.3", "description": "Check if a path is inside another path", "license": "MIT", "repository": "sindresorhus/is-path-inside", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["path", "inside", "folder", "directory", "dir", "file", "resolve"], "devDependencies": {"ava": "^2.1.0", "tsd": "^0.7.2", "xo": "^0.24.0"}}